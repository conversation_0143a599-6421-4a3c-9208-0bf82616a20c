<template>
  <div class="dashboard">
    <!-- 顶部操作栏 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="page-title">JiangJiangWorld</h1>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Upload" @click="handleUpload">
          上传文件
        </el-button>
        <el-button :icon="FolderAdd" @click="showCreateFolderDialog">
          新建文件夹
        </el-button>
        <div class="view-toggle">
          <el-button
            :type="viewMode === 'grid' ? 'primary' : ''"
            :icon="Grid"
            @click="viewMode = 'grid'"
            circle
          />
          <el-button
            :type="viewMode === 'list' ? 'primary' : ''"
            :icon="List"
            @click="viewMode = 'list'"
            circle
          />
        </div>
      </div>
    </div>

    <!-- 快速访问区域 -->
    <div class="quick-access">
      <h2 class="section-title">快速访问</h2>
      <div class="access-cards">
        <div class="access-card" @click="navigateTo('/recent')">
          <div class="card-icon recent">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="card-content">
            <h3 class="card-title">最近文件</h3>
            <p class="card-count">{{ recentCount }} 个文件</p>
          </div>
        </div>

        <div class="access-card" @click="navigateTo('/files')">
          <div class="card-icon starred">
            <el-icon><Star /></el-icon>
          </div>
          <div class="card-content">
            <h3 class="card-title">常用文件</h3>
            <p class="card-count">{{ starredCount }} 个文件</p>
          </div>
        </div>

        <div class="access-card" @click="navigateTo('/files')">
          <div class="card-icon files">
            <el-icon><Folder /></el-icon>
          </div>
          <div class="card-content">
            <h3 class="card-title">我的文件</h3>
            <p class="card-count">{{ filesCount }} 个文件</p>
          </div>
        </div>

        <div class="access-card" @click="navigateTo('/shares')">
          <div class="card-icon shared">
            <el-icon><Share /></el-icon>
          </div>
          <div class="card-content">
            <h3 class="card-title">我的分享</h3>
            <p class="card-count">{{ sharesCount }} 个分享</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件列表 -->
    <div class="file-section">
      <div class="section-header">
        <h2 class="section-title">文件名</h2>
        <div class="section-actions">
          <span class="file-count">大小</span>
          <span class="file-count">修改日期</span>
          <span class="file-count">操作</span>
        </div>
      </div>

      <div class="file-list" v-loading="loading">
        <div
          v-for="file in fileList"
          :key="file.id"
          class="file-item"
          @click="handleFileClick(file)"
          @dblclick="handleFileDoubleClick(file)"
        >
          <div class="file-info">
            <div class="file-icon-wrapper">
              <el-icon class="file-icon" :style="{ color: getFileTypeColor(file.name, file.type === 'folder') }">
                <component :is="getFileIconComponent(file.name, file.type === 'folder')" />
              </el-icon>
            </div>
            <span class="file-name">{{ file.name }}</span>
          </div>
          <div class="file-size">
            {{ file.type === 'folder' ? '-' : formatFileSize(file.size) }}
          </div>
          <div class="file-date">
            {{ formatTime(file.updated_at) }}
          </div>
          <div class="file-actions">
            <el-dropdown @command="(command) => handleFileCommand(command, file)" @click.stop>
              <el-button :icon="More" text />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="file.type === 'file'" command="download">
                    <el-icon><Download /></el-icon>
                    下载
                  </el-dropdown-item>
                  <el-dropdown-item command="share" v-if="file.type === 'file'">
                    <el-icon><Share /></el-icon>
                    分享
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && fileList.length === 0" class="empty-state">
          <el-icon class="empty-icon"><FolderOpened /></el-icon>
          <p class="empty-text">这里还没有文件</p>
          <p class="empty-desc">上传文件或创建文件夹开始使用</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Upload,
  FolderAdd,
  Grid,
  List,
  Clock,
  Star,
  Folder,
  Share,
  More,
  Download,
  Delete,
  FolderOpened,
  Document,
  Picture,
  VideoCamera,
  Headphones,
} from '@element-plus/icons-vue'
import { useFileStore } from '@/store/file'
import { getFileList, deleteFile } from '@/api/file'
import { formatFileSize, formatTime, getFileIcon, getFileTypeColor } from '@/utils'
import type { FileItem } from '@/types'

const router = useRouter()
const fileStore = useFileStore()

// 响应式数据
const loading = ref(false)
const fileList = ref<FileItem[]>([])
const viewMode = ref<'grid' | 'list'>('list')

// 统计数据
const recentCount = ref(12)
const starredCount = ref(8)
const filesCount = ref(24)
const sharesCount = ref(5)

// 获取文件图标组件
const getFileIconComponent = (fileName: string, isFolder: boolean) => {
  const iconName = getFileIcon(fileName, isFolder)
  const iconMap: Record<string, any> = {
    folder: Folder,
    'folder-zip': FolderOpened,
    document: Document,
    picture: Picture,
    'video-camera': VideoCamera,
    headphones: Headphones,
  }
  return iconMap[iconName] || Document
}

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path)
}

// 处理上传
const handleUpload = () => {
  // TODO: 实现上传功能
  ElMessage.info('上传功能开发中...')
}

// 显示创建文件夹对话框
const showCreateFolderDialog = () => {
  // TODO: 实现创建文件夹功能
  ElMessage.info('创建文件夹功能开发中...')
}

// 处理文件点击
const handleFileClick = (file: FileItem) => {
  // TODO: 实现文件选择
}

// 处理文件双击
const handleFileDoubleClick = (file: FileItem) => {
  if (file.type === 'folder') {
    router.push({ path: '/files', query: { folder: file.id } })
  } else {
    handleFileCommand('download', file)
  }
}

// 处理文件命令
const handleFileCommand = async (command: string, file: FileItem) => {
  switch (command) {
    case 'download':
      if (file.type === 'file') {
        window.open(`/api/files/${file.id}/download`, '_blank')
      }
      break
    case 'share':
      // TODO: 实现分享功能
      ElMessage.info('分享功能开发中...')
      break
    case 'delete':
      try {
        await deleteFile(file.id)
        ElMessage.success('删除成功')
        loadFileList()
      } catch (error) {
        console.error('删除文件失败:', error)
        ElMessage.error('删除失败')
      }
      break
  }
}

// 加载文件列表
const loadFileList = async () => {
  try {
    loading.value = true
    const response = await getFileList({
      parent_id: undefined,
      page: 1,
      page_size: 10,
      order_by: 'updated_at',
      order: 'desc',
    })
    fileList.value = response.list
  } catch (error) {
    console.error('加载文件列表失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadFileList()
})
</script>

<style scoped>
.dashboard {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #252836;
  border-radius: 12px;
  border: 1px solid #2d3142;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.view-toggle {
  display: flex;
  gap: 4px;
  margin-left: 12px;
}

/* 快速访问区域 */
.quick-access {
  padding: 20px 24px;
  background: #252836;
  border-radius: 12px;
  border: 1px solid #2d3142;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.access-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.access-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.access-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #6366f1;
  transform: translateY(-2px);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #ffffff;
}

.card-icon.recent {
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
}

.card-icon.starred {
  background: linear-gradient(135deg, #eab308 0%, #facc15 100%);
}

.card-icon.files {
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
}

.card-icon.shared {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
}

.card-count {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
}

/* 文件列表区域 */
.file-section {
  flex: 1;
  background: #252836;
  border-radius: 12px;
  border: 1px solid #2d3142;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #2d3142;
}

.section-actions {
  display: flex;
  gap: 120px;
  font-size: 14px;
  color: #9ca3af;
  font-weight: 500;
}

.file-list {
  flex: 1;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  border-bottom: 1px solid rgba(45, 49, 66, 0.5);
  cursor: pointer;
  transition: background 0.3s ease;
}

.file-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
}

.file-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-icon {
  font-size: 20px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  width: 100px;
  font-size: 14px;
  color: #9ca3af;
  text-align: right;
}

.file-date {
  width: 150px;
  font-size: 14px;
  color: #9ca3af;
  text-align: right;
}

.file-actions {
  width: 60px;
  display: flex;
  justify-content: center;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #9ca3af;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  margin: 0 0 8px 0;
}

.empty-desc {
  font-size: 14px;
  margin: 0;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: center;
  }

  .access-cards {
    grid-template-columns: 1fr;
  }

  .section-actions {
    display: none;
  }

  .file-size,
  .file-date {
    display: none;
  }

  .file-actions {
    width: auto;
  }
}
</style>
